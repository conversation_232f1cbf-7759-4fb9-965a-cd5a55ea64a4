# Changelog

Combined changelog for GoLogin python SDK

## [2025.7.7] 2025-07-16


### Fixes

* Languages are now passing correctly based on ip and proxy timezone

## [2025.7.6] 2025-07-14


### Fixes

* Profile with empty archive and no preferences file now can be opened

## [2025.7.5] 2025-07-05


### Fixes

* Custom extensions now are downloadable through the SDK

## [2025.7.4] 2025-07-02


### Fixes

* Fixed error with some socks5 proxy

## [2025.7.3] 2025-06-17


### Improvements

* Browser resolution now inherits from profile settings

## [2025.7.2] 2025-06-16


### Improvements

* Added support for 135 version of orbita with new proxy schema

## [2025.7.1] 2025-06-13


### Bug fixes

* Fixed error of profile run without proxy
* Added small errors descriptions

## [2025.5.2134227] 2025-05-02


### Bug fixes

* Fixed cookies compitability with run from application

## [2025.4.22172740] 2025-04-22


### Miscellaneous Chores

* Deleted old exmaples and added new one that up to date
* Added changelog to track changes
