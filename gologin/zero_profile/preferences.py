zeroProfilePreferences = {
  'NewTabPage': {
    'PrevNavigationTime': '*****************',
  },
  'account_id_migration_state': 2,
  'account_tracker_service_last_update': '*****************',
  'ack_existing_ntp_extensions': True,
  'alternate_error_pages': {
    'backup': True,
  },
  'apps': {},
  'autocomplete': {
    'retention_policy_last_version': 103,
  },
  'autofill': {
    'orphan_rows_removed': True,
  },
  'bookmark_bar': {
    'show_on_all_tabs': False,
  },
  'browser': {
    'has_seen_welcome_page': False,
    'window_placement': {
      'bottom': 875,
      'left': 22,
      'maximized': False,
      'right': 1222,
      'top': 60,
      'work_area_bottom': 897,
      'work_area_left': 0,
      'work_area_right': 1512,
      'work_area_top': 38,
    },
  },
  'countryid_at_install': 21077,
  'custom_links': {
    'initialized': True,
    'list': [{
      'isMostVisited': False,
      'title': 'Facebook',
      'url': 'https://www.facebook.com/',
    }, {
      'isMostVisited': False,
      'title': 'Google Ads',
      'url': 'https://ads.google.com/',
    }, {
      'isMostVisited': False,
      'title': 'TikTok',
      'url': 'https://www.tiktok.com/',
    }, {
      'isMostVisited': False,
      'title': 'Amazon',
      'url': 'https://www.amazon.com/',
    }, {
      'isMostVisited': False,
      'title': 'eBay',
      'url': 'https://ebay.com/',
    }, {
      'isMostVisited': False,
      'title': 'YouTube',
      'url': 'https://www.youtube.com/',
    }, {
      'isMostVisited': False,
      'title': 'Coinlist',
      'url': 'https://coinlist.co/',
    }, {
      'isMostVisited': False,
      'title': 'Huobi',
      'url': 'https://www.huobi.com/',
    }, {
      'isMostVisited': False,
      'title': 'bet365',
      'url': 'https://www.bet365.com/',
    }, {
      'isMostVisited': False,
      'title': 'PayPal',
      'url': 'https://paypal.com/',
    }],
  },
  'default_apps_install_state': 3,
  'domain_diversity': {
    'last_reporting_timestamp': '*****************',
  },
  'extensions': {
    'alerts': {
      'initialized': True,  
    },
    'chrome_url_overrides': {
    },
    'last_chrome_version': '103.0.5060.53',
    'settings': {},
  },
  'gaia_cookie': {
    'changed_time': **********.108506,
    'hash': '2jmj7l5rSw0yVb/vlWAYkK/YBwk=',
    'last_list_accounts_data': '["gaia.l.a.r",[]]',
  },
  'gcm': {
    'product_category_for_subtypes': 'com.orbita.macosx',
  },
  'google': {
    'services': {
      'signin_scoped_device_id': 'e8046704-b3cb-4533-8099-e2261bc1be67',
    },
  },
  'intl': {
    'selected_languages': 'en-US,en',
  },
  'invalidation': {
    'per_sender_topics_to_handler': {
      '*************': {
      },
      '**********': {
      },
    },
  },
  'media': {
    'device_id_salt': '',
    'engagement': {
      'schema_version': 5,
    },
  },
  'media_router': {
    'receiver_id_hash_token': '',
  },
  'ntp': {
    'num_personal_suggestions': 1,
  },
  'optimization_guide': {
    'previously_registered_optimization_types': {
      'ABOUT_THIS_SITE': True,
      'HISTORY_CLUSTERS': True,
    },
    'store_file_paths_to_delete': {
    },
  },
  'plugins': {
    'plugins_list': [],
  },
  'privacy_sandbox': {
    'preferences_reconciled': True,
  },
  'profile': {
    'avatar_bubble_tutorial_shown': 2,
    'avatar_index': 26,
    'content_settings': {
      'enable_quiet_permission_ui_enabling_method': {
        'notifications': 1,
      },
      'exceptions': {
        'accessibility_events': {},
        'app_banner': {},
        'ar': {},
        'auto_select_certificate': {},
        'automatic_downloads': {},
        'autoplay': {},
        'background_sync': {},
        'bluetooth_chooser_data': {},
        'bluetooth_guard': {},
        'bluetooth_scanning': {},
        'camera_pan_tilt_zoom': {},
        'client_hints': {},
        'clipboard': {},
        'cookies': {},
        'durable_storage': {},
        'fedcm_active_session': {},
        'fedcm_share': {},
        'file_system_access_chooser_data': {},
        'file_system_last_picked_directory': {},
        'file_system_read_guard': {},
        'file_system_write_guard': {},
        'formfill_metadata': {},
        'geolocation': {},
        'get_display_media_set_select_all_screens': {},
        'hid_chooser_data': {},
        'hid_guard': {},
        'http_allowed': {},
        'idle_detection': {},
        'images': {},
        'important_site_info': {},
        'insecure_private_network': {},
        'installed_web_app_metadata': {},
        'intent_picker_auto_display': {},
        'javascript': {},
        'javascript_jit': {},
        'legacy_cookie_access': {},
        'local_fonts': {},
        'media_engagement': {},
        'media_stream_camera': {},
        'media_stream_mic': {},
        'midi_sysex': {},
        'mixed_script': {},
        'nfc_devices': {},
        'notifications': {},
        'password_protection': {},
        'payment_handler': {},
        'permission_autoblocking_data': {},
        'permission_autorevocation_data': {},
        'popups': {},
        'ppapi_broker': {},
        'protocol_handler': {},
        'safe_browsing_url_check_data': {},
        'sensors': {},
        'serial_chooser_data': {},
        'serial_guard': {},
        'site_engagement': {},
        'sound': {},
        'ssl_cert_decisions': {},
        'storage_access': {},
        'subresource_filter': {},
        'subresource_filter_data': {},
        'usb_chooser_data': {},
        'usb_guard': {},
        'vr': {},
        'webid_api': {},
        'window_placement': {},
      },
      'pref_version': 1,
    },
    'created_by_version': '103.0.5060.53',
    'creation_time': '***********310804',
    'exit_type': 'Normal',
    'last_engagement_time': '*****************',
    'last_time_password_store_metrics_reported': **********.573382,
    'managed_user_id': '',
    'name': 'Person 1',
    'password_account_storage_settings': {
    },
  },
  'safebrowsing': {
    'event_timestamps': {
    },
    'metrics_last_log_time': '***********',
  },
  'signin': {
    'allowed': False,
  },
  'sync': {
    'requested': False,
  },
  'translate_site_blacklist': [],
  'translate_site_blacklist_with_time': {
  },
  'unified_consent': {
    'migration_state': 10,
  },
  'web_apps': {
    'system_web_app_failure_count': 0,
    'system_web_app_last_attempted_language': 'en-US',
    'system_web_app_last_attempted_update': '103.0.5060.53',
    'system_web_app_last_installed_language': 'en-US',
    'system_web_app_last_update': '103.0.5060.53',
  },
  'webauthn': {
    'touchid': {
      'metadata_secret': 'FAs08eDqvux1A4NYorVc4ZHDwnhqyLknX9ef3JS4DLg=',
    },
  },
};