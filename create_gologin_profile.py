#!/usr/bin/env python3
"""
Gologin Profile Creator Script

This script creates a new Gologin profile using a first name and last name as input.
It returns the profile ID of the newly created profile.

Usage:
    python create_gologin_profile.py --token <token> --os <os> <first_name> <last_name>

Example:
    python create_gologin_profile.py --token <token> --os <os> <first_name> <last_name>

Requirements:
    - gologin package installed (pip install gologin)
    - Valid Gologin API token
"""

import argparse
import sys

from gologin import GoLogin


def create_profile(first_name, last_name, token, os_type="mac"):
    """
    Create a new Gologin profile with the given first and last name.

    Args:
        first_name (str): First name for the profile
        last_name (str): Last name for the profile
        token (str): Gologin API token
        os_type (str): Operating system type ('lin', 'win', 'mac'). Default: 'mac'

    Returns:
        str: Profile ID of the newly created profile

    Raises:
        Exception: If profile creation fails
    """
    # Initialize GoLogin with token
    gl = GoLogin({"token": token})

    # Create profile name from first and last name
    profile_name = f"{first_name} {last_name}"

    try:
        # Create profile with random fingerprint
        profile = gl.createProfileRandomFingerprint(
            {"os": os_type, "name": profile_name}
        )

        # Extract profile ID from response
        profile_id = profile.get("id")

        if not profile_id:
            raise Exception("Failed to create profile: No profile ID returned")

        return profile_id

    except Exception as e:
        raise Exception(f"Failed to create Gologin profile: {str(e)}")


def main():
    """Main function to handle command line arguments and create profile."""
    parser = argparse.ArgumentParser(
        description="Create a new Gologin profile with first and last name",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python create_gologin_profile.py --token <token> --os <os> <first_name> <last_name>
    python create_gologin_profile.py --token YOUR_TOKEN --os win Jane Smith
    python create_gologin_profile.py --help
        """,
    )

    parser.add_argument("first_name", required=True, help="First name for the profile")
    parser.add_argument("last_name", required=True, help="Last name for the profile")
    parser.add_argument(
        "--token",
        required=True,
        help="Gologin API token",
    )
    parser.add_argument(
        "--os",
        choices=["lin", "win", "mac"],
        default="mac",
        help="Operating system type for the profile (default: mac)",
    )

    args = parser.parse_args()
    token = args.token

    try:
        # Create the profile
        profile_id = create_profile(
            first_name=args.first_name,
            last_name=args.last_name,
            token=token,
            os_type=args.os,
        )

        # Output the profile ID
        print("Profile created successfully!")
        print(f"Profile Name: {args.first_name} {args.last_name}")
        print(f"Profile ID: {profile_id}")
        print(f"OS Type: {args.os}")

        return profile_id

    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
