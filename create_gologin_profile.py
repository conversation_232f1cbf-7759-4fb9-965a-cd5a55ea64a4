#!/usr/bin/env python3
"""
Gologin Profile Creator Script with Automatic Proxidize Proxy Assignment

This script creates a new Gologin profile using a first name, last name, and OS as input.
It returns the profile ID of the newly created profile.
It also automatically assigns the "Proxidize" proxy to the profile.

Usage:
    python create_gologin_profile.py --token <token> --os <os> <first_name> <last_name>

Example:
    python create_gologin_profile.py --token <token> --os <os> <first_name> <last_name>

Requirements:
    - gologin package installed (pip install gologin)
    - Valid Gologin API token
    - "Proxidize" proxy in your proxy list
"""

import sys
import time

from gologin import GoLogin
from gologin.http_client import make_request


def get_proxy_by_name(token, proxy_name):
    """
    Get proxy details by name from the user's proxy list.

    Args:
        token (str): Gologin API token
        proxy_name (str): Name of the proxy to find

    Returns:
        dict: Proxy details if found, None otherwise
    """
    headers = {
        "Authorization": f"Bearer {token}",
        "User-Agent": "gologin-api",
        "Content-Type": "application/json",
    }

    try:
        # Get list of all proxies
        response = make_request("GET", "https://api.gologin.com/proxy", headers=headers)

        if response.status_code == 200:
            proxies_res = response.json()

            # Search for proxy by name
            for proxy in proxies_res["proxies"]:
                if proxy.get("customName", "").lower() == proxy_name.lower():
                    return proxy

            print(f"Warning: Proxy '{proxy_name}' not found in your proxy list")
            print("Available proxies:")
            for proxy in proxies_res["proxies"]:
                print(
                    f"  - {proxy.get('customName', 'Unnamed')} (ID: {proxy.get('id', 'N/A')})"
                )
            return None
        else:
            print(f"Failed to get proxy list: {response.status_code}")
            return None

    except Exception as e:
        print(f"Error getting proxy list: {str(e)}")
        return None


def create_profile(first_name, last_name, token, os_type="mac"):
    """
    Create a new Gologin profile with the given first and last name.
    Automatically assigns the "Proxidize" proxy to the profile.

    Args:
        first_name (str): First name for the profile
        last_name (str): Last name for the profile
        token (str): Gologin API token
        os_type (str): Operating system type ('lin', 'win', 'mac'). Default: 'mac'

    Returns:
        str: Profile ID of the newly created profile

    Raises:
        Exception: If profile creation fails
    """
    # Initialize GoLogin with token
    gl = GoLogin({"token": token})

    # Create profile name from first and last name
    profile_name = f"{first_name} {last_name}"

    try:
        # Create profile with random fingerprint
        profile = gl.createProfileRandomFingerprint(
            {"os": os_type, "name": profile_name}
        )

        # Extract profile ID from response
        profile_id = profile.get("id")

        if not profile_id:
            raise Exception("Failed to create profile: No profile ID returned")

        # Try to assign Proxidize proxy
        try:
            proxidize_proxy = get_proxy_by_name(token, "Proxidize")

            if proxidize_proxy:
                # Assign the proxy to the profile
                proxy_data = {
                    "mode": proxidize_proxy.get("mode", "http"),
                    "host": proxidize_proxy.get("host"),
                    "port": proxidize_proxy.get("port"),
                    "username": proxidize_proxy.get("username", ""),
                    "password": proxidize_proxy.get("password", ""),
                }

                status_code = gl.changeProfileProxy(profile_id, proxy_data)

                if status_code == 204:
                    print(
                        f"Successfully assigned Proxidize proxy to profile {profile_id}"
                    )
                else:
                    print(
                        f"Warning: Failed to assign proxy (status code: {status_code})"
                    )
            else:
                print(
                    "Warning: Proxidize proxy not found, profile created without proxy"
                )

        except Exception as proxy_error:
            print(f"Warning: Failed to assign Proxidize proxy: {str(proxy_error)}")
            print("Profile created successfully but without proxy")

        return profile_id

    except Exception as e:
        raise Exception(f"Failed to create Gologin profile: {str(e)}")


def get_names_list():
    return [
        {"first_name": "Théo", "last_name": "Petit"},
        {"first_name": "Manon", "last_name": "Durand"},
        {"first_name": "Antoine", "last_name": "Leroy"},
        {"first_name": "Camille", "last_name": "Moreau"},
        {"first_name": "Clément", "last_name": "Simon"},
        {"first_name": "Julie", "last_name": "Laurent"},
        {"first_name": "Maxime", "last_name": "Michel"},
        {"first_name": "Sarah", "last_name": "Garcia"},
        {"first_name": "Alexandre", "last_name": "Martinez"},
        {"first_name": "Emma", "last_name": "David"},
        {"first_name": "Nicolas", "last_name": "Bertrand"},
        {"first_name": "Lola", "last_name": "Roux"},
        {"first_name": "Julien", "last_name": "Vincent"},
        {"first_name": "Inès", "last_name": "Fournier"},
        {"first_name": "Pierre", "last_name": "Morel"},
        {"first_name": "Léna", "last_name": "Girard"},
        {"first_name": "Quentin", "last_name": "André"},
        {"first_name": "Zoé", "last_name": "Lefèvre"},
        {"first_name": "Baptiste", "last_name": "Mercier"},
        {"first_name": "Alice", "last_name": "Dupont"},
        {"first_name": "Enzo", "last_name": "Lambert"},
        {"first_name": "Margaux", "last_name": "Bonnet"},
        {"first_name": "Mathis", "last_name": "François"},
        {"first_name": "Jade", "last_name": "Arnaud"},
        {"first_name": "Tom", "last_name": "Caron"},
        {"first_name": "Louise", "last_name": "Rousseau"},
        {"first_name": "Arthur", "last_name": "Lemoine"},
        {"first_name": "Romane", "last_name": "Gauthier"},
        {"first_name": "Gabin", "last_name": "Henry"},
        {"first_name": "Lina", "last_name": "Masson"},
        {"first_name": "Nathan", "last_name": "Perrin"},
        {"first_name": "Rose", "last_name": "Marchand"},
        {"first_name": "Sacha", "last_name": "Aubry"},
        {"first_name": "Anna", "last_name": "Joly"},
        {"first_name": "Louis", "last_name": "Renard"},
        {"first_name": "Mila", "last_name": "Colin"},
        {"first_name": "Raphaël", "last_name": "Legrand"},
        {"first_name": "Eva", "last_name": "Poirier"},
        {"first_name": "Adam", "last_name": "Rodriguez"},
        {"first_name": "Louna", "last_name": "Renaud"},
        {"first_name": "Maël", "last_name": "Schmitt"},
        {"first_name": "Ambre", "last_name": "Bourgeois"},
        {"first_name": "Noé", "last_name": "Léger"},
        {"first_name": "Alix", "last_name": "Chevalier"},
        {"first_name": "Timéo", "last_name": "Muller"},
        {"first_name": "Agathe", "last_name": "Guerin"},
        {"first_name": "Kylian", "last_name": "Fernandez"},
        {"first_name": "Clara", "last_name": "Lopez"},
        {"first_name": "Eliott", "last_name": "Fontaine"},
        {"first_name": "Sara", "last_name": "Robin"},
        {"first_name": "Léon", "last_name": "Gillet"},
        {"first_name": "Léonie", "last_name": "Roy"},
        {"first_name": "Axel", "last_name": "Navarro"},
        {"first_name": "Thaïs", "last_name": "Maillard"},
        {"first_name": "Naël", "last_name": "Brun"},
        {"first_name": "Inaya", "last_name": "Meyer"},
        {"first_name": "Malo", "last_name": "Blanchard"},
        {"first_name": "Lyana", "last_name": "Morin"},
        {"first_name": "Ilyès", "last_name": "Deschamps"},
        {"first_name": "Yasmine", "last_name": "Clement"},
        {"first_name": "Nino", "last_name": "Gaillard"},
        {"first_name": "Elsa", "last_name": "Barbier"},
        {"first_name": "Aaron", "last_name": "Huet"},
        {"first_name": "Lila", "last_name": "Rey"},
        {"first_name": "Yanis", "last_name": "Olivier"},
        {"first_name": "Nour", "last_name": "Poulain"},
        {"first_name": "Sohan", "last_name": "Boucher"},
        {"first_name": "Lya", "last_name": "Garnier"},
        {"first_name": "Evan", "last_name": "Besson"},
        {"first_name": "Tess", "last_name": "Delorme"},
        {"first_name": "Noa", "last_name": "Marin"},
        {"first_name": "Lana", "last_name": "Valentin"},
        {"first_name": "Milo", "last_name": "Bouvet"},
        {"first_name": "Joy", "last_name": "Perrot"},
        {"first_name": "Ayden", "last_name": "Carlier"},
        {"first_name": "Zélie", "last_name": "Charles"},
        {"first_name": "Ibrahim", "last_name": "Dufour"},
        {"first_name": "Livia", "last_name": "Pelletier"},
        {"first_name": "Swan", "last_name": "Leclerc"},
        {"first_name": "Mia", "last_name": "Denis"},
        {"first_name": "Ayoub", "last_name": "Dumont"},
        {"first_name": "Alma", "last_name": "Baumann"},
        {"first_name": "Oscar", "last_name": "Louis"},
        {"first_name": "Charlie", "last_name": "Riviere"},
        {"first_name": "Imran", "last_name": "Adam"},
        {"first_name": "Capucine", "last_name": "Prevost"},
        {"first_name": "Wassim", "last_name": "Perez"},
        {"first_name": "Léana", "last_name": "Carpentier"},
        {"first_name": "Alessio", "last_name": "Maury"},
        {"first_name": "Lyna", "last_name": "Lemoignan"},
        {"first_name": "Tito", "last_name": "Guillaume"},
        {"first_name": "Maëlys", "last_name": "Jacquet"},
        {"first_name": "Ismaël", "last_name": "Bailly"},
        {"first_name": "Lilou", "last_name": "Hubert"},
        {"first_name": "Kenzo", "last_name": "Jean"},
        {"first_name": "Thalia", "last_name": "Collet"},
        {"first_name": "Aylan", "last_name": "Vidal"},
        {"first_name": "Assia", "last_name": "Leveque"},
        {"first_name": "Malone", "last_name": "Benoit"},
        {"first_name": "Soline", "last_name": "Gilles"},
        {"first_name": "Aurelien", "last_name": "Leblanc"},
        {"first_name": "Elina", "last_name": "Jourdain"},
        {"first_name": "Soan", "last_name": "Tesson"},
        {"first_name": "Maelle", "last_name": "Mathe"},
        {"first_name": "Amin", "last_name": "Devaux"},
        {"first_name": "Elena", "last_name": "Berge"},
        {"first_name": "Tyméo", "last_name": "Lagarde"},
        {"first_name": "Lise", "last_name": "Pasquier"},
        {"first_name": "Amir", "last_name": "Rolland"},
        {"first_name": "Diane", "last_name": "Hebert"},
        {"first_name": "Ayoub", "last_name": "Grondin"},
        {"first_name": "Salomé", "last_name": "Picard"},
    ]


def main():
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NjdkNzc1NWJmZmYwZDliZDViOTZhMmYiLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2N2U1ODRhNWY4MTg2Y2ZkZTgzYTg5OTQifQ.wo1YDG0QT6MgO1Qc0aqRV4PqXUsj2h_gOuinSsWTEBA"

    try:
        names_list = get_names_list()

        for name_dict in names_list:
            # Create the profile
            profile_id = create_profile(
                first_name=name_dict["first_name"],
                last_name=name_dict["last_name"],
                token=token,
                os_type="mac",
            )

            # Output the profile ID
            print("Profile created successfully!")
            print(f"Profile Name: {name_dict['first_name']} {name_dict['last_name']}")
            print(f"Profile ID: {profile_id}")
            print("Proxidize proxy assignment attempted automatically")
            print("-" * 80)
            time.sleep(1)

    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
