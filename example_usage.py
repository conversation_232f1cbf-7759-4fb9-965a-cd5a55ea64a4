#!/usr/bin/env python3
"""
Example usage of the Gologin profile creation script.

This demonstrates how to use the create_gologin_profile module
both as a standalone script and as an imported module.
"""

from create_gologin_profile import create_profile


def example_usage():
    """Example of how to use the create_profile function."""
    
    # Your Gologin API token
    # Get your token from: https://app.gologin.com/#/personalArea/TokenApi
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NjdkNzc1NWJmZmYwZDliZDViOTZhMmYiLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2N2U1ODRhNWY4MTg2Y2ZkZTgzYTg5OTQifQ.wo1YDG0QT6MgO1Qc0aqRV4PqXUsj2h_gOuinSsWTEBA"
    
    # Example 1: Create a Linux profile
    try:
        profile_id = create_profile(
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>", 
            token=token,
            os_type="lin"
        )
        print(f"Created Linux profile: {profile_id}")
    except Exception as e:
        print(f"Error creating Linux profile: {e}")
    
    # Example 2: Create a Windows profile
    try:
        profile_id = create_profile(
            first_name="<PERSON>",
            last_name="<PERSON>",
            token=token,
            os_type="win"
        )
        print(f"Created Windows profile: {profile_id}")
    except Exception as e:
        print(f"Error creating Windows profile: {e}")
    
    # Example 3: Create a Mac profile
    try:
        profile_id = create_profile(
            first_name="Bob",
            last_name="Johnson",
            token=token,
            os_type="mac"
        )
        print(f"Created Mac profile: {profile_id}")
    except Exception as e:
        print(f"Error creating Mac profile: {e}")


if __name__ == "__main__":
    example_usage()
