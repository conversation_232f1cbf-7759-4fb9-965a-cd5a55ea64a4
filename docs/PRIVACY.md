# Privacy Policy

## Data Collection

### Error Reporting

We collect error information to improve the stability and performance of our application. Error data is sent to our self-hosted Sentry instance

**What we collect:**
- Error logs and stack traces only when application errors occur
- Basic hardware information (CPU architecture, memory usage)
- Software environment details (operating system, browser version)
- Application state at the time of error

**What we don't collect:**
- Personal user information
- User credentials or authentication data
- Private user content or files
- Browsing history or personal usage patterns

**When we collect data:**
- Only when application errors occur
- No continuous monitoring or tracking
- Data collection is limited to error scenarios

**Data storage:**
- All error data is stored on our self-hosted infrastructure
- Data is not shared with third-party services
- Error logs are retained for debugging and improvement purposes only

**How to disable error reporting:**
- Set environment variable: `DISABLE_TELEMETRY=false`

## Contact

If you have questions about this privacy policy or our data practices, please contact us.
