import asyncio

from playwright.async_api import async_playwright

from gologin import GoLogin


async def main():
    gl = GoLogin(
        {
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NjdkNzc1NWJmZmYwZDliZDViOTZhMmYiLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2N2U1ODRhNWY4MTg2Y2ZkZTgzYTg5OTQifQ.wo1YDG0QT6MgO1Qc0aqRV4PqXUsj2h_gOuinSsWTEBA",
            "profile_id": "6634f2f1c64999e5bc97d15a",
        }
    )

    debugger_address = gl.start()
    async with async_playwright() as p:
        browser = await p.chromium.connect_over_cdp("http://" + debugger_address)
        default_context = browser.contexts[0]
        page = default_context.pages[0]
        await page.goto("https://linkedin.com")
        await page.screenshot(path="linkedin.png")
        await page.close()
    gl.stop()


asyncio.run(main())
