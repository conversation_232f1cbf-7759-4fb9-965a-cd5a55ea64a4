# CLAUDE.md

This file provides guidance to <PERSON> + (claude.ai/code) when working with code in this + repository.

## Project Overview

pygologin is the official Python SDK for + GoLogin - a browser automation and + fingerprinting service that helps run browser + profiles with unique fingerprints for Selenium, + Playwright, and Puppeteer automation.

## Key Commands

### Testing

```bash
# Run all tests
export GL_API_TOKEN="your_token"
export GL_PROFILE_ID="your_profile_id"
python test/run_tests.py

# Run specific test file
python test/test_specific.py
```

### Development Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

### Building and Publishing

```bash
# Build distribution
python setup.py sdist bdist_wheel

# Upload to PyPI (done via GitHub Actions)
# Manual trigger: Actions → publish → Run
          + workflow
```

## Architecture Overview

### Core Components

1. **gologin/gologin.py** (1284 lines) - Main + GoLogin class implementing:

   - Browser profile lifecycle management
   - Proxy configuration and validation
   - Cookie handling
   - Extension loading
   - WebSocket URL generation for automation + tools

2. **Manager Pattern** - Specialized managers + for different concerns:

   - `browserManager/` - Browser binary + downloads and management
   - `cookiesManager/` - Cookie + encryption/decryption and persistence
   - `extensionsManager/` - Chrome extension + loading

3. **Integration Pattern**:
   ```python
   # Context manager pattern
   with GoLogin(config) as gl:
       ws_url = gl.start()
       # Connect automation tool to ws_url
   # Automatically stops and saves profile
   ```

### Key Design Decisions

1. **Temporary Profile Storage**: Profiles are + downloaded to temp directories and uploaded
   back
   after use
2. **Proxy Handling**: Supports HTTP, SOCKS5, + and GoLogin's built-in proxies with + authentication
3. **Telemetry**: Sentry integration for error + tracking (disable with
   `DISABLE_TELEMETRY=true`)
4. **Cross-Platform**: Special handling for + Windows paths and processes

### Important Files to Know

- `gologin/golgoin_types.py` - Type
  definitions
  for API responses
- `gologin/http_client.py` - Centralized HTTP + client with retry logic
- `zero_profile/` - Default browser profile + templates
- `examples/` - Working examples for Selenium, + Playwright, Puppeteer

### Common Development Tasks

When modifying the SDK:

1. Check `gologin/gologin.py` for the main + logic
2. Test changes with examples in `examples/` + directory
3. Ensure compatibility with all three + automation frameworks
4. Run tests with valid API credentials

### API Integration

The SDK communicates with GoLogin API at:

- Main API: `https://api.gologin.com`
- Proxy API: `https://api.goproxy.gologin.com`

Key endpoints:

- `/profiles/{id}` - Profile management
- `/profiles/{id}/web` - Start browser session
- `/browser/cookies` - Cookie operations
